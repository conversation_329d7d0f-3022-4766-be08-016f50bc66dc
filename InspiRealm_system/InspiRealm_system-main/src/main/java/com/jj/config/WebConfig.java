package com.jj.config;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jj.common.constant.CommonConstant;
import com.jj.common.exception.BizException;
import com.jj.common.response.JsonResult;
import com.jj.core.constant.RedisKeyConstant;
import com.jj.core.interceptor.AdminAuthInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.config.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Configuration
@EnableWebMvc
@Slf4j
@ControllerAdvice
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private AdminAuthInterceptor adminAuthInterceptor;





    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册管理员权限拦截器
        registry.addInterceptor(adminAuthInterceptor)
                .addPathPatterns("/api/admin/**")
                .excludePathPatterns("/api/admin/auth/login"); // 排除登录接口
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");


    }



    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(60000);
    }

    @ResponseBody
    @ExceptionHandler(value = {Exception.class})
    public JsonResult errorHandler(Exception ex) {
        //记录异常日志
        if (ex instanceof MethodArgumentNotValidException ||
                ex instanceof BindException) {
            //验证类异常 不报警
            log.warn("warning occurs:", ex);
        } else {
            log.error("error occurs:", ex);
        }
        JsonResult r;
        if (ex instanceof BizException) {
            BizException bizException = (BizException) ex;
            r = JsonResult.error(((BizException) ex).getMessageCode(), ex.getMessage());
        } else if (ex instanceof BindException) {
            FieldError fieldError =  ((BindException)ex).getBindingResult().getFieldError();
            assert fieldError != null;
            String bindErrorField =fieldError.getField();
            String bindErrorMessage = fieldError.getDefaultMessage();
            r = JsonResult.error("参数转化异常:" + bindErrorField + " " + bindErrorMessage);
        } else if (ex instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException) ex;
            List<ObjectError> errors = validException.getBindingResult().getAllErrors();
            if(!errors.isEmpty()){
                r = JsonResult.error(errors.get(0).getDefaultMessage());
            }else {
                r = JsonResult.error("MethodArgumentNotValidException");
            }

        } else {
            r = JsonResult.error(ex.getMessage());
        }
        return r;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        return new RedisCacheManager(
                RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory),
                this.getRedisCacheConfigurationWithTtl(30*60), // 默认策略，未配置的 key 会使用这个
                this.getRedisCacheConfigurationMap() // 指定 key 策略
        );
    }

    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>();
        redisCacheConfigurationMap.put(CommonConstant.LOG_TRACE_ID, this.getRedisCacheConfigurationWithTtl(60 * 60));
        redisCacheConfigurationMap.put(RedisKeyConstant.TOKEN, this.getRedisCacheConfigurationWithTtl(60 * 60 * 24 * 7));
        return redisCacheConfigurationMap;
    }

    private RedisCacheConfiguration getRedisCacheConfigurationWithTtl(Integer seconds) {
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);

        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig();
        redisCacheConfiguration = redisCacheConfiguration.serializeValuesWith(
                RedisSerializationContext
                        .SerializationPair
                        .fromSerializer(jackson2JsonRedisSerializer)
        ).entryTtl(Duration.ofSeconds(seconds));

        return redisCacheConfiguration;
    }



}