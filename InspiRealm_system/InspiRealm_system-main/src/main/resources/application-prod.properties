# ==========================================
# InspiRealm System - 开发环境配置
# ==========================================

# 应用基础配置
spring.application.name=InspiRealm_system
server.port=9908
server.servlet.context-path=/
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

# ==========================================
# 数据库配置
# ==========================================
spring.datasource.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
spring.datasource.url=*************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username=InspiRealm_system
spring.datasource.password=123000aaa...

# MyBatis Plus配置
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.jj.dao.model
mybatis.config-location=classpath:sqlmapconfig.xml

# ==========================================
# Redis缓存配置
# ==========================================
spring.redis.database=6
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=123000aaa...
spring.redis.timeout=60s
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-active=300
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=20

# ==========================================
# 微信小程序配置
# ==========================================
wechat.app-id=wxde4eac8820c130cf
wechat.app-secret=e059eb151de01b31cf96722f8b5454aa

# ==========================================
# 积分系统配置
# ==========================================
points.register-reward=50
points.daily-checkin=50
points.image-process-cost=10

# ==========================================
# AI图片处理配置
# ==========================================
ai.sora-image-api-url=https://yunwu.ai/v1/chat/completions
ai.sora-image-api-key=sk-HsIcesXofJ1XU62bnIG3RHhnXUgJElh425MZsKQlE053bN0v
# AI API超时配置（毫秒）
ai.connect-timeout=6000000
ai.read-timeout=6000000

# ==========================================
# MinIO文件存储配置
# ==========================================
minio.endpoint=https://file.jdctools.com.cn
minio.access-key=${MINIO_ACCESS_KEY:admin}
minio.secret-key=${MINIO_SECRET_KEY:123000aaa...A}
minio.bucket-name=inspirealm-dev
minio.port=443
minio.secure=true

# ==========================================
# 其他配置
# ==========================================
jdc.domain=localhost
