package com.jj.core.manager;

import com.jj.common.exception.BizException;
import io.minio.*;
import io.minio.http.Method;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.time.LocalDateTime;
import com.jj.core.utils.DateFormatUtil;

import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MinIO文件管理器
 * 负责文件的上传、下载、删除和缩略图生成
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Component
public class MinioManager {
    
    private static final Logger logger = LoggerFactory.getLogger(MinioManager.class);
    
    @Value("${minio.endpoint:}")
    private String endpoint;
    
    @Value("${minio.access-key:}")
    private String accessKey;
    
    @Value("${minio.secret-key:}")
    private String secretKey;
    
    @Value("${minio.bucket-name:inspirealm}")
    private String bucketName;
    
    private MinioClient minioClient;
    
    /**
     * 缩略图尺寸
     */
    private static final int THUMBNAIL_WIDTH = 300;
    private static final int THUMBNAIL_HEIGHT = 300;
    
    /**
     * 支持的图片格式
     */
    private static final String[] SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    /**
     * MinIO客户端是否已初始化
     */
    private volatile boolean initialized = false;

    @PostConstruct
    public void init() {
        try {
            // 初始化MinIO客户端（不检查连接）
            minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            logger.info("MinIO客户端配置完成，endpoint: {}, bucket: {}", endpoint, bucketName);

            // 尝试连接并检查bucket（非阻塞）
            tryInitializeBucket();

        } catch (Exception e) {
            logger.error("MinIO客户端配置失败", e);
            // 不抛出异常，允许应用继续启动
            logger.warn("MinIO服务暂时不可用，将在首次使用时重试初始化");
        }
    }

    /**
     * 尝试初始化bucket（非阻塞）
     */
    private void tryInitializeBucket() {
        try {
            // 检查存储桶是否存在，不存在则创建
            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                logger.info("创建MinIO存储桶成功: {}", bucketName);
            }

            initialized = true;
            logger.info("MinIO客户端初始化成功，endpoint: {}, bucket: {}", endpoint, bucketName);

        } catch (Exception e) {
            if ("Access denied".equals(e.getMessage())){
                initialized = true;
                logger.info("MinIO客户端初始化成功，endpoint: {}, bucket: {}", endpoint, bucketName);
                return;
            }
            logger.warn("MinIO bucket初始化失败，将在使用时重试: {}", e.getMessage());
            initialized = false;
        }
    }

    /**
     * 确保MinIO已初始化
     */
    private void ensureInitialized() {
        if (!initialized && minioClient != null) {
            synchronized (this) {
                if (!initialized) {
                    tryInitializeBucket();
                }
            }
        }

        if (!initialized) {
            throw new BizException("MinIO服务不可用，请检查配置和服务状态");
        }
    }

    /**
     * 从URL下载图片并上传到MinIO
     * 
     * @param imageUrl 图片URL
     * @param userId 用户ID
     * @return 上传结果
     */
    public MinioUploadResult uploadImageFromUrl(String imageUrl, Long userId) {
        try {
            // 确保MinIO已初始化
            ensureInitialized();

            logger.info("开始从URL下载并上传图片，imageUrl: {}, userId: {}", imageUrl, userId);
            
            // 下载图片
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            if (image == null) {
                throw new BizException("无法读取图片内容");
            }
            
            // 生成文件名
            String fileName = generateFileName(userId, "jpg");
            String objectName = "images/" + fileName;
            
            // 将BufferedImage转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", baos);
            byte[] imageBytes = baos.toByteArray();
            
            // 上传到MinIO
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(bais, imageBytes.length, -1)
                            .contentType("image/jpeg")
                            .build()
            );
            
            // 生成缩略图
            String thumbnailObjectName = generateThumbnail(image, userId);
            
            // 构建结果
            MinioUploadResult result = new MinioUploadResult();
            result.setSuccess(true);
            result.setFileName(fileName);
            result.setObjectName(objectName);
            result.setImageUrl(getFileUrl(objectName));
            result.setThumbnailUrl(getFileUrl(thumbnailObjectName));
            result.setFileSize((long) imageBytes.length);
            result.setImageWidth(image.getWidth());
            result.setImageHeight(image.getHeight());
            result.setImageFormat("jpg");
            
            logger.info("图片上传成功，fileName: {}, size: {}", fileName, imageBytes.length);
            return result;
            
        } catch (Exception e) {
            logger.error("从URL上传图片失败", e);
            throw new BizException("图片上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 从Base64上传图片到MinIO
     * 
     * @param imageBase64 图片Base64
     * @param userId 用户ID
     * @return 上传结果
     */
    public MinioUploadResult uploadImageFromBase64(String imageBase64, Long userId) {
        try {
            // 确保MinIO已初始化
            ensureInitialized();

            logger.info("开始从Base64上传图片，userId: {}", userId);
            
            // 解析Base64
            String[] parts = imageBase64.split(",");
            if (parts.length != 2) {
                throw new BizException("Base64格式不正确");
            }
            
            String header = parts[0]; // data:image/png;base64
            String data = parts[1];   // 实际的base64数据
            
            // 提取图片格式
            String format = extractFormatFromBase64Header(header);
            if (!isSupportedFormat(format)) {
                throw new BizException("不支持的图片格式: " + format);
            }
            
            // 解码Base64
            byte[] imageBytes = java.util.Base64.getDecoder().decode(data);
            
            // 读取图片
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bais);
            if (image == null) {
                throw new BizException("无法读取图片内容");
            }
            
            // 生成文件名
            String fileName = generateFileName(userId, format);
            String objectName = "images/" + fileName;
            
            // 上传到MinIO
            bais = new ByteArrayInputStream(imageBytes);
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(bais, imageBytes.length, -1)
                            .contentType("image/" + format)
                            .build()
            );
            
            // 生成缩略图
            String thumbnailObjectName = generateThumbnail(image, userId);
            
            // 构建结果
            MinioUploadResult result = new MinioUploadResult();
            result.setSuccess(true);
            result.setFileName(fileName);
            result.setObjectName(objectName);
            result.setImageUrl(getFileUrl(objectName));
            result.setThumbnailUrl(getFileUrl(thumbnailObjectName));
            result.setFileSize((long) imageBytes.length);
            result.setImageWidth(image.getWidth());
            result.setImageHeight(image.getHeight());
            result.setImageFormat(format);
            
            logger.info("图片上传成功，fileName: {}, size: {}", fileName, imageBytes.length);
            return result;
            
        } catch (Exception e) {
            logger.error("从Base64上传图片失败", e);
            throw new BizException("图片上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成缩略图
     * 
     * @param originalImage 原图
     * @param userId 用户ID
     * @return 缩略图对象名
     */
    private String generateThumbnail(BufferedImage originalImage, Long userId) {
        try {
            // 计算缩略图尺寸
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            
            int thumbnailWidth = THUMBNAIL_WIDTH;
            int thumbnailHeight = THUMBNAIL_HEIGHT;
            
            // 保持宽高比
            double ratio = Math.min((double) thumbnailWidth / originalWidth, (double) thumbnailHeight / originalHeight);
            thumbnailWidth = (int) (originalWidth * ratio);
            thumbnailHeight = (int) (originalHeight * ratio);
            
            // 创建缩略图
            BufferedImage thumbnail = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = thumbnail.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(originalImage, 0, 0, thumbnailWidth, thumbnailHeight, null);
            g2d.dispose();
            
            // 生成缩略图文件名
            String thumbnailFileName = generateFileName(userId, "jpg", "thumb");
            String thumbnailObjectName = "thumbnails/" + thumbnailFileName;
            
            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(thumbnail, "jpg", baos);
            byte[] thumbnailBytes = baos.toByteArray();
            
            // 上传缩略图
            ByteArrayInputStream bais = new ByteArrayInputStream(thumbnailBytes);
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(thumbnailObjectName)
                            .stream(bais, thumbnailBytes.length, -1)
                            .contentType("image/jpeg")
                            .build()
            );
            
            logger.info("缩略图生成成功，fileName: {}", thumbnailFileName);
            return thumbnailObjectName;
            
        } catch (Exception e) {
            logger.error("生成缩略图失败", e);
            // 缩略图生成失败不影响主流程
            return null;
        }
    }

    /**
     * 删除文件
     *
     * @param objectName 对象名
     */
    public void deleteFile(String objectName) {
        try {
            // 确保MinIO已初始化
            ensureInitialized();

            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            logger.info("文件删除成功，objectName: {}", objectName);
        } catch (Exception e) {
            logger.error("文件删除失败，objectName: {}", objectName, e);
            // 删除失败不抛异常，避免影响主流程
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param objectName 对象名
     * @return 访问URL
     */
    public String getFileUrl(String objectName) {
        try {
            // 确保MinIO已初始化
            ensureInitialized();

            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(7, TimeUnit.DAYS)
                            .build()
            );
        } catch (Exception e) {
            logger.error("获取文件URL失败，objectName: {}", objectName, e);
            return null;
        }
    }

    /**
     * 生成文件名
     *
     * @param userId 用户ID
     * @param format 文件格式
     * @return 文件名
     */
    private String generateFileName(Long userId, String format) {
        return generateFileName(userId, format, null);
    }

    /**
     * 生成文件名
     *
     * @param userId 用户ID
     * @param format 文件格式
     * @param prefix 前缀
     * @return 文件名
     */
    private String generateFileName(Long userId, String format, String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        StringBuilder fileName = new StringBuilder();
        if (prefix != null && !prefix.trim().isEmpty()) {
            fileName.append(prefix).append("_");
        }
        fileName.append("user_").append(userId)
                .append("_").append(timestamp)
                .append("_").append(uuid)
                .append(".").append(format);

        return fileName.toString();
    }

    /**
     * 从Base64头部提取图片格式
     *
     * @param header Base64头部
     * @return 图片格式
     */
    private String extractFormatFromBase64Header(String header) {
        // data:image/png;base64 -> png
        if (header.contains("image/")) {
            String[] parts = header.split("image/");
            if (parts.length > 1) {
                String formatPart = parts[1];
                if (formatPart.contains(";")) {
                    return formatPart.split(";")[0];
                }
                return formatPart;
            }
        }
        return "jpg"; // 默认格式
    }

    /**
     * 检查是否支持的图片格式
     *
     * @param format 图片格式
     * @return 是否支持
     */
    private boolean isSupportedFormat(String format) {
        if (format == null) {
            return false;
        }
        for (String supportedFormat : SUPPORTED_FORMATS) {
            if (supportedFormat.equalsIgnoreCase(format)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查MinIO服务状态
     *
     * @return 服务状态
     */
    public boolean isHealthy() {
        if (minioClient == null) {
            return false;
        }

        try {
            // 尝试检查bucket存在性来验证连接
            minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            return true;
        } catch (Exception e) {
            logger.debug("MinIO健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取MinIO服务状态信息
     *
     * @return 状态信息
     */
    public String getStatusInfo() {
        if (minioClient == null) {
            return "未配置";
        }

        if (initialized) {
            return "正常";
        } else {
            return "连接失败";
        }
    }

    /**
     * 检查URL是否为MinIO地址
     *
     * @param url 图片URL
     * @return 是否为MinIO地址
     */
    public boolean isMinioUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        // 检查URL是否包含MinIO的endpoint
        return url.contains(endpoint);
    }

    /**
     * 从MinIO URL中提取objectName
     *
     * @param url MinIO URL
     * @return objectName，如果提取失败返回null
     */
    public String extractObjectNameFromUrl(String url) {
        if (!isMinioUrl(url)) {
            return null;
        }

        try {
            // MinIO的预签名URL格式通常为：
            // https://endpoint/bucket/objectName?X-Amz-Algorithm=...
            // 我们需要提取bucket后面的objectName部分

            String urlWithoutParams = url.split("\\?")[0]; // 去掉查询参数
            String bucketPrefix = "/" + bucketName + "/";

            int bucketIndex = urlWithoutParams.indexOf(bucketPrefix);
            if (bucketIndex != -1) {
                return urlWithoutParams.substring(bucketIndex + bucketPrefix.length());
            }

            logger.warn("无法从URL中提取objectName: {}", url);
            return null;

        } catch (Exception e) {
            logger.error("提取objectName失败，url: {}", url, e);
            return null;
        }
    }

    /**
     * 删除MinIO文件（通过URL）
     *
     * @param url MinIO文件URL
     * @return 是否删除成功
     */
    public boolean deleteFileByUrl(String url) {
        if (!isMinioUrl(url)) {
            logger.warn("URL不是MinIO地址，跳过删除: {}", url);
            return false;
        }

        String objectName = extractObjectNameFromUrl(url);
        if (objectName == null) {
            logger.warn("无法从URL提取objectName，跳过删除: {}", url);
            return false;
        }

        try {
            deleteFile(objectName);
            logger.info("通过URL删除文件成功，url: {}, objectName: {}", url, objectName);
            return true;
        } catch (Exception e) {
            logger.error("通过URL删除文件失败，url: {}, objectName: {}", url, objectName, e);
            return false;
        }
    }

    /**
     * MinIO上传结果
     */
    public static class MinioUploadResult {

        private boolean success;
        private String fileName;
        private String objectName;
        private String imageUrl;
        private String thumbnailUrl;
        private Long fileSize;
        private Integer imageWidth;
        private Integer imageHeight;
        private String imageFormat;
        private String errorMessage;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getObjectName() {
            return objectName;
        }

        public void setObjectName(String objectName) {
            this.objectName = objectName;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getThumbnailUrl() {
            return thumbnailUrl;
        }

        public void setThumbnailUrl(String thumbnailUrl) {
            this.thumbnailUrl = thumbnailUrl;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public Integer getImageWidth() {
            return imageWidth;
        }

        public void setImageWidth(Integer imageWidth) {
            this.imageWidth = imageWidth;
        }

        public Integer getImageHeight() {
            return imageHeight;
        }

        public void setImageHeight(Integer imageHeight) {
            this.imageHeight = imageHeight;
        }

        public String getImageFormat() {
            return imageFormat;
        }

        public void setImageFormat(String imageFormat) {
            this.imageFormat = imageFormat;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "MinioUploadResult{" +
                    "success=" + success +
                    ", fileName='" + fileName + '\'' +
                    ", objectName='" + objectName + '\'' +
                    ", imageUrl='" + imageUrl + '\'' +
                    ", thumbnailUrl='" + thumbnailUrl + '\'' +
                    ", fileSize=" + fileSize +
                    ", imageWidth=" + imageWidth +
                    ", imageHeight=" + imageHeight +
                    ", imageFormat='" + imageFormat + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    '}';
        }
    }
}
