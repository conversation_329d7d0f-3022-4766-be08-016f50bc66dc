autoDetectedPackages:
- com.jj.common.constant
- com.jj.common.enums
- com.jj.common.exception
- com.jj.common.response
- com.jj.common.utils
- com.jj.common.vo
- com.jj.config
- com.jj.core.annotation
- com.jj.core.auth
- com.jj.core.config
- com.jj.core.constant
- com.jj.core.domain
- com.jj.core.interceptor
- com.jj.core.manager
- com.jj.core.service
- com.jj.core.utils
- com.jj.dao.mapper
- com.jj.dao.model
- com.jj.dao.utils
- com.jj.main
- com.jj.web.admin
- com.jj.web.annotation
- com.jj.web.aspect
- com.jj.web.wechat
enableAutoDetect: true
entryDisplayConfig:
  excludedPathPatterns: []
  skipJsCss: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: false
  skipNonProjectPackages: false
  skipPrivateMethods: false
  skipSetters: false
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
name: xcodemap-filter
recordMode: all
sourceDisplayConfig:
  color: blue
startOnDebug: false
